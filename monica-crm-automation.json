{"nodes": [{"parameters": {}, "id": "d63453b0-e08a-4077-a53c-1b9cde90fc86", "name": "Every 5 Minutes", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [-1060, -120]}, {"parameters": {"language": "python", "pythonCode": "# Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor item in _input.all():\n  item.json.myNewField = 1\nreturn _input.all()"}, "id": "********-2565-4255-8dae-1f92541e8e88", "name": "Filter Today's Notes", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, -120]}, {"parameters": {"resource": "note", "operation": "getAll", "returnAll": true}, "type": "n8n-nodes-base.monicaCrm", "typeVersion": 1, "position": [-840, -120], "id": "8e365722-c836-4b10-af97-1ecb5b618fff", "name": "Monica CRM", "credentials": {"monicaCrmApi": {"id": "6MMSQFhcc01kWBmv", "name": "Monica CRM account"}}}], "connections": {"Every 5 Minutes": {"main": [[{"node": "Monica CRM", "type": "main", "index": 0}]]}, "Monica CRM": {"main": [[{"node": "Filter Today's Notes", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}}