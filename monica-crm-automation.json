{"nodes": [{"parameters": {}, "id": "d63453b0-e08a-4077-a53c-1b9cde90fc86", "name": "Every 5 Minutes", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [-1060, -120]}, {"parameters": {"language": "python", "pythonCode": "from datetime import datetime\n\n# Get today's date in YYYY-MM-DD format\ntoday = datetime.now().strftime('%Y-%m-%d')\n\n# Filter notes that were updated today\nfiltered_notes = []\n\nfor item in _input.all():\n    note = item.json\n    \n    # Get the updated_at date (or created_at if updated_at doesn't exist)\n    note_date = None\n    if 'updated_at' in note and note['updated_at']:\n        note_date = note['updated_at'][:10]  # Extract YYYY-MM-DD part\n    elif 'created_at' in note and note['created_at']:\n        note_date = note['created_at'][:10]  # Extract YYYY-MM-DD part\n    \n    # Only include notes from today\n    if note_date == today:\n        filtered_notes.append(item)\n\n# Return filtered notes\nreturn filtered_notes"}, "id": "********-2565-4255-8dae-1f92541e8e88", "name": "Filter Today's Notes", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-600, -120]}, {"parameters": {"resource": "note", "operation": "getAll", "returnAll": true}, "type": "n8n-nodes-base.monicaCrm", "typeVersion": 1, "position": [-840, -120], "id": "8e365722-c836-4b10-af97-1ecb5b618fff", "name": "Monica CRM", "credentials": {"monicaCrmApi": {"id": "6MMSQFhcc01kWBmv", "name": "Monica CRM account"}}}, {"parameters": {"language": "python", "pythonCode": "# Group notes by contact and combine them\ncontact_notes = {}\n\nfor item in _input.all():\n    note = item.json\n    contact_id = note.get('contact_id')\n    \n    if contact_id not in contact_notes:\n        contact_notes[contact_id] = {\n            'contact_id': contact_id,\n            'notes': [],\n            'contact_name': note.get('contact', {}).get('complete_name', 'Unknown Contact')\n        }\n    \n    # Add note content to the list\n    if 'body' in note and note['body']:\n        contact_notes[contact_id]['notes'].append(note['body'])\n\n# Create output items with combined notes for each contact\nresult = []\nfor contact_id, data in contact_notes.items():\n    if data['notes']:  # Only process contacts with notes\n        combined_notes = '\\n\\n'.join(data['notes'])\n        result.append({\n            'json': {\n                'contact_id': contact_id,\n                'contact_name': data['contact_name'],\n                'combined_notes': combined_notes,\n                'notes_count': len(data['notes'])\n            }\n        })\n\nreturn result"}, "id": "combine-notes-by-contact", "name": "Combine Notes by Contact", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-360, -120]}, {"parameters": {"model": "gpt-4o-mini", "options": {"systemMessage": "# Role (R)\nYou are an AI assistant skilled in summarizing meeting notes with a focus on client interactions and follow-up actions.\n\n# Task (T)\nYour task is to analyze the provided meeting notes and generate a **structured json** summary divided into four sections: **Summary**, **Tasks**, **Email**, and **Reminder&Calls**.\n\n# Instructions (I)\n- **Summary:** Provide a concise summary of all topics discussed with the client(s), only including relevant client-related content.\n- **Tasks:** List all actionable items or ToDos derived from the meeting, excluding any instructions related to contacting people via call or email.\n- **Email:** Extract details about emailing individuals mentioned in the notes, including personal information such as first name, last name, and any other identifying details relevant to the email recipient. (Mattermost internally, externally just working with reminders & summary)\n- **Reminder&Calls:** Identify people who need to be contacted by phone or scheduled for meetings, specifying who to contact, the reason for the call or meeting, and any pertinent details.\n- If some information is not found, keep that **part empty**.\n\nReturn only valid JSON in this exact format:\n{\n  \"Summary\": \"string\",\n  \"Tasks\": [\"task1\", \"task2\"],\n  \"Email\": [{\n    \"first_name\": \"string\",\n    \"last_name\": \"string\",\n    \"details\": \"string\"\n  }],\n  \"ReminderCalls\": [{\n    \"contact_person\": \"string\",\n    \"reason\": \"string\",\n    \"details\": \"string\"\n  }]\n}"}, "prompt": "Please analyze these meeting notes and provide a structured summary:\n\n{{ $json.combined_notes }}"}, "id": "analyze-notes-llm", "name": "Analyze Notes with LLM", "type": "n8n-nodes-base.openAi", "typeVersion": 1.3, "position": [-120, -120], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API Key"}}}, {"parameters": {"language": "python", "pythonCode": "import json\nfrom datetime import datetime\n\n# Parse the LLM response\nresult = []\n\nfor item in _input.all():\n    try:\n        # Get the LLM response content\n        llm_response = item.json.get('message', {}).get('content', '')\n        \n        # Parse the JSON response from LLM\n        analysis_result = json.loads(llm_response)\n        \n        # Get the original contact data from previous nodes\n        contact_data = None\n        for prev_item in _input.all():\n            if 'contact_id' in prev_item.json:\n                contact_data = prev_item.json\n                break\n        \n        if not contact_data:\n            continue\n            \n        # Prepare the structured data\n        parsed_data = {\n            'contact_id': contact_data['contact_id'],\n            'contact_name': contact_data['contact_name'],\n            'today_date': datetime.now().strftime('%Y-%m-%d'),\n            'summary': analysis_result.get('Summary', ''),\n            'tasks': analysis_result.get('Tasks', []),\n            'emails': analysis_result.get('Email', []),\n            'reminder_calls': analysis_result.get('ReminderCalls', []),\n            'original_notes': contact_data['combined_notes']\n        }\n        \n        result.append({'json': parsed_data})\n        \n    except json.JSONDecodeError as e:\n        print(f\"Failed to parse LLM response as JSON: {e}\")\n        continue\n    except Exception as e:\n        print(f\"Error processing item: {e}\")\n        continue\n\nreturn result"}, "id": "parse-llm-response", "name": "Parse LLM Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [120, -120]}], "connections": {"Every 5 Minutes": {"main": [[{"node": "Monica CRM", "type": "main", "index": 0}]]}, "Monica CRM": {"main": [[{"node": "Filter Today's Notes", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}}