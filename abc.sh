#!/bin/bash

# Define the Docker image to pull
DOCKER_IMAGE="nvcr.io/nvidia/cuda:12.9.0-cudnn-devel-ubuntu24.04"

# Define the image size in gigabytes (you'd need to know this beforehand or get it dynamically)
# For this example, we'll use the provided size.
IMAGE_SIZE_GB=5.97

echo "Pulling Docker image: $DOCKER_IMAGE"
echo "Image size (known): $IMAGE_SIZE_GB GB"

# Run the docker pull command and capture its stderr (where 'time' output goes)
# and its stdout (the docker pull messages)
# We use a temporary file to store the stderr output from 'time'
TIME_OUTPUT=$( (time docker pull "$DOCKER_IMAGE") 2>&1 >/dev/null )

# Use awk to find the line starting with "real" and extract the time value
# Convert 'XmY.ZZZs' format to total seconds
TIME_STRING=$(echo "$TIME_OUTPUT" | awk '/real/ {print $2}')

# Parse minutes and seconds from the time string (e.g., "2m46.641s")
# Remove 'm' and 's' characters and split into minutes and seconds
MINUTES=$(echo "$TIME_STRING" | sed 's/m.*//')
SECONDS_DECIMAL=$(echo "$TIME_STRING" | sed 's/.*m//' | sed 's/s//')

# Calculate total time in seconds
TIME_TAKEN_SECONDS=$(echo "$MINUTES * 60 + $SECONDS_DECIMAL" | bc -l)

# Convert image size from GB to bytes
# 1 GB = 1024^3 bytes (using 1024 for file size calculations is common)
IMAGE_SIZE_BYTES=$(echo "$IMAGE_SIZE_GB * 1024 * 1024 * 1024" | bc)

# Calculate bandwidth in bytes per second
BANDWIDTH_BPS=$(echo "$IMAGE_SIZE_BYTES / $TIME_TAKEN_SECONDS" | bc -l)

# Convert bandwidth from bytes per second to Megabits per second (Mbps)
# 1 byte = 8 bits
# 1 Megabit = 1,000,000 bits (standard for network speeds)
BANDWIDTH_MBPS=$(echo "$BANDWIDTH_BPS * 8 / 1000000" | bc -l)

# Print the results
echo "----------------------------------------"
echo "Download details:"
echo "Time taken (real): $TIME_TAKEN_SECONDS seconds"
printf "Image size: %.2f GB (%.0f bytes)\n" "$IMAGE_SIZE_GB" "$IMAGE_SIZE_BYTES"
printf "Estimated download bandwidth: %.2f Mbps\n" "$BANDWIDTH_MBPS"
echo "----------------------------------------"