#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --all-extras --output-file=requirements_dev.txt pyproject.toml
#
annotated-types==0.7.0
    # via pydantic
build==1.2.2.post1
    # via pip-tools
certifi==2025.1.31
    # via requests
charset-normalizer==3.4.1
    # via requests
click==8.2.1
    # via pip-tools
idna==3.10
    # via requests
iniconfig==2.0.0
    # via pytest
packaging==24.2
    # via
    #   build
    #   pytest
pip-tools==7.4.1
    # via gpu-on-demand-manager (pyproject.toml)
pluggy==1.5.0
    # via pytest
pydantic==2.10.6
    # via gpu-on-demand-manager (pyproject.toml)
pydantic-core==2.27.2
    # via pydantic
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   gpu-on-demand-manager (pyproject.toml)
    #   pytest-dotenv
pytest-dotenv==0.5.2
    # via gpu-on-demand-manager (pyproject.toml)
python-dotenv==1.0.1
    # via pytest-dotenv
requests==2.32.3
    # via gpu-on-demand-manager (pyproject.toml)
typing-extensions==4.12.2
    # via
    #   pydantic
    #   pydantic-core
urllib3==2.3.0
    # via requests
wheel==0.45.1
    # via pip-tools

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
