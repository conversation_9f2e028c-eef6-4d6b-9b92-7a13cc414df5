# GPU On-Demand Manager

## Setup

### **Specify new Dependencies**

Our whole Python setup is based on the pyproject.toml specification.
Do not touch the requirements*.txt files as they are automatically generated from pyproject.toml using the following command:
```bash
pip-compile pyproject.toml --all-extras --output-file requirements_dev.txt
```

or 

```bash
pip-compile pyproject.toml --output-file requirements.txt
```

If you would like to add or change a Python dependency, just change the dependencies inside the pyproject.toml and call the command from above.

Afterwards, you can install the specified packages using `pip install -r requirements_dev.txt` from within dev environment or `pip install -r requirements.txt` for the production environment.