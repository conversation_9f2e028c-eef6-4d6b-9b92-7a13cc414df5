import requests
from pydantic import ValidationError
from typing import List, Dict, Any
from itertools import product
import pandas as pd

from gpu_on_demand_manager.models.shadeform.instances_types import (
    APIResponse as InstancesTypesAPIResponse,
)
import os 
from dotenv import load_dotenv

load_dotenv()
API_KEY = os.getenv("SHADEFORM_API_KEY")

class ShadeformSDK:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.default_headers = {"X-API-KEY": f"{api_key}"}

    def _generate_expanded_rows(self, instance_types: List[Any]) -> List[Dict[str, Any]]:
        """Generate rows for each combination of instance type, OS option, and region."""
        rows = []
        for instance in instance_types:
            os_options = instance.configuration.os_options
            regions = instance.availability

            for os_option, region_info in product(os_options, regions):
                country = region_info.display_name.split(",")[0].strip()

                row = {
                    "cloud": instance.cloud,
                    "region": region_info.region,
                    "shade_instance_type": instance.shade_instance_type,
                    "cloud_instance_type": instance.cloud_instance_type,
                    "memory_in_gb": instance.configuration.memory_in_gb,
                    "storage_in_gb": instance.configuration.storage_in_gb,
                    "vcpus": instance.configuration.vcpus,
                    "num_gpus": instance.configuration.num_gpus,
                    "gpu_type": instance.configuration.gpu_type,
                    "interconnect": instance.configuration.interconnect,
                    "vram_per_gpu_in_gb": instance.configuration.vram_per_gpu_in_gb,
                    "os_option": os_option,
                    "hourly_price": instance.hourly_price,
                    "deployment_type": instance.deployment_type,
                    "availability_region": region_info.region,
                    "available": region_info.available,
                    "display_name": region_info.display_name,
                    "country": country,
                    "min_boot_in_sec": instance.boot_time.min_boot_in_sec,
                    "max_boot_in_sec": instance.boot_time.max_boot_in_sec,
                    "nvlink": instance.nvlink
                }
                rows.append(row)
        return rows

    def check_availability(self):
        url = "https://api.shadeform.ai/v1/instances/types"
        response = requests.request(
            "GET",
            url,
            headers=self.default_headers,
        )

        if response.status_code == 200:
            try:
                data = response.json()
                parsed_response = InstancesTypesAPIResponse.model_validate(data)
                
                # Generate expanded rows
                expanded_rows = self._generate_expanded_rows(parsed_response.instance_types)
                
                # Create DataFrame and save to CSV
                df = pd.DataFrame(expanded_rows)
                df.to_csv("all_gpu_instances.csv", index=False)
                
                return df  # Return DataFrame for potential further use
                
            except ValidationError as e:
                raise Exception(f"Validation Error: {e}")
        else:
            raise Exception(f"Failed to fetch data: {response.status_code} {response.text}")

if __name__ == "__main__":
    sdk = ShadeformSDK(api_key=API_KEY)
    sdk.check_availability()