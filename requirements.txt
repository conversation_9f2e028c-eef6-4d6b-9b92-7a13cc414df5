#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --output-file=requirements.txt pyproject.toml
#
annotated-types==0.7.0
    # via pydantic
build==1.2.2.post1
    # via pip-tools
certifi==2025.1.31
    # via requests
charset-normalizer==3.4.1
    # via requests
click==8.2.1
    # via pip-tools
idna==3.10
    # via requests
packaging==25.0
    # via build
pip-tools==7.4.1
    # via gpu-on-demand-manager (pyproject.toml)
pydantic==2.10.6
    # via gpu-on-demand-manager (pyproject.toml)
pydantic-core==2.27.2
    # via pydantic
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
requests==2.32.3
    # via gpu-on-demand-manager (pyproject.toml)
typing-extensions==4.12.2
    # via
    #   pydantic
    #   pydantic-core
urllib3==2.3.0
    # via requests
wheel==0.45.1
    # via pip-tools
