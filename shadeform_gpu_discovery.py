import requests
from pydantic import ValidationError

from gpu_on_demand_manager.models.shadeform.instances_types import (
    APIResponse as InstancesTypesAPIResponse,
)
from dotenv import load_dotenv
load_dotenv()
import os 

API_KEY = os.getenv('SHADEFORM_API_KEY')


class ShadeformSDK:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.default_headers = {"X-API-KEY": f"{api_key}"}

    def check_availability(self):
        url = "https://api.shadeform.ai/v1/instances/types"
        response = requests.request(
            "GET",
            url,
            params={
                "gpu_type": "A6000",
                "availability": True,
                "num_gpus": 1,
                "sort": "price",
            },
            headers=self.default_headers,
        )

        if response.status_code == 200:
            try:
                data = response.json()  # Convert response to dictionary
                parsed_response = InstancesTypesAPIResponse.model_validate(
                    data
                )  # Validate and parse data
                print(parsed_response)
                num_instances = len(parsed_response.instance_types)
                cheapest_instance = parsed_response.instance_types[0]
                most_expensive_instance = parsed_response.instance_types[
                    num_instances - 1
                ]
                print(f"Cheapest gpu: {cheapest_instance.hourly_price}")
                print(f"Most expensive gpu: {most_expensive_instance.hourly_price}")
            except ValidationError as e:
                print("Validation Error:", e)
        else:
            print("Failed to fetch data:", response.status_code, response.text)


if __name__ == "__main__":
    sdk = ShadeformSDK(api_key=API_KEY)
    sdk.check_availability()
