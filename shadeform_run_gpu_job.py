# How to:

# 1. Discover available gpus --> shadeform_gpu_discovery.py
# https://docs.shadeform.ai/getting-started/quickstart#finding-a-gpu-instance
# 2. Launch a VM with direct GPU access
# https://docs.shadeform.ai/getting-started/quickstart#launching-the-instance
# 3. Start docker container from image using Shadeforms Rest-API
# https://docs.shadeform.ai/guides/dockercontainers#basic-example
# 4. SSHing into the instance using private key from Shadeform account
# https://docs.shadeform.ai/getting-started/quickstart#sshing-into-the-instance
# Direct access to VM over SSH possible (no serverless in this sense)
#
