import pandas as pd
import os
import sys
from typing import Optional, Dict, Any, List
import click
from tabulate import tabulate
import json

class GPUProviderSelector:
    def __init__(self, csv_file: Optional[str] = None):
        self.df = None
        self.filtered_df = None
        self.csv_file = csv_file
        
        # Sample data for demo
        self.sample_data = {
            'cloud': ['latitude', 'latitude'],
            'region': ['frankfurt-germany-4', 'frankfurt-germany-5'],
            'shade_instance_type': ['A100_80Gx8', 'A100_80Gx8'],
            'cloud_instance_type': ['g3-a100-large', 'g3-a100-large'],
            'memory_in_gb': [1536, 1536],
            'storage_in_gb': [15200, 15200],
            'vcpus': [128, 128],
            'num_gpus': [8, 8],
            'gpu_type': ['A100_80G', 'A100_80G'],
            'interconnect': ['pcie', 'pcie'],
            'vram_per_gpu_in_gb': [80, 80],
            'os_option': ['ubuntu22.04_cuda12.4_shade_os', 'ubuntu22.04_cuda12.4_shade_os'],
            'hourly_price': [82800, 82800],  # Stored as cents
            'deployment_type': ['baremetal', 'baremetal'],
            'availability_region': ['frankfurt-germany-4', 'frankfurt-germany-5'],
            'available': [True, False],
            'display_name': ['DE, Frankfurt', 'DE, Frankfurt'],
            'country': ['DE', 'DE'],
            'min_boot_in_sec': [300, 300],
            'max_boot_in_sec': [600, 600],
            'nvlink': [True, True]
        }
    
    def load_csv(self, file_path: str) -> bool:
        """Load CSV file and return success status"""
        try:
            if not os.path.exists(file_path):
                click.echo(f"❌ Error: File '{file_path}' not found.")
                return False
            
            self.df = pd.read_csv(file_path)
            # Convert hourly_price from cents to dollars for internal processing
            if 'hourly_price' in self.df.columns:
                self.df['hourly_price'] = self.df['hourly_price'] / 100.0
            self.filtered_df = self.df.copy()
            click.echo(f"✅ Successfully loaded {len(self.df)} GPU instances from '{file_path}'")
            return True
        except Exception as e:
            click.echo(f"❌ Error loading CSV: {str(e)}")
            return False
    
    def load_sample_data(self):
        """Load sample data for demonstration"""
        self.df = pd.DataFrame(self.sample_data)
        # Convert hourly_price from cents to dollars
        self.df['hourly_price'] = self.df['hourly_price'] / 100.0
        self.filtered_df = self.df.copy()
        click.echo("✅ Sample data loaded successfully!")
        click.echo(f"📊 Loaded {len(self.df)} sample GPU instances")
    
    def show_summary(self):
        """Display summary statistics of the dataset"""
        if self.df is None:
            click.echo("❌ No data loaded. Please load a CSV file first.")
            return
        
        click.echo("\n" + "="*60)
        click.echo("📊 DATASET SUMMARY")
        click.echo("="*60)
        
        click.echo(f"Total instances: {len(self.df)}")
        click.echo(f"Available instances: {len(self.df[self.df['available'] == True])}")
        click.echo(f"Unavailable instances: {len(self.df[self.df['available'] == False])}")
        
        if 'gpu_type' in self.df.columns:
            click.echo("\nGPU Types:")
            gpu_counts = self.df['gpu_type'].value_counts()
            for gpu, count in gpu_counts.items():
                click.echo(f"  • {gpu}: {count} instances")
        
        if 'display_name' in self.df.columns:
            click.echo("\nRegions:")
            region_counts = self.df['display_name'].value_counts()
            for region, count in region_counts.items():
                click.echo(f"  • {region}: {count} instances")
        
        if 'hourly_price' in self.df.columns:
            click.echo("\nPrice Range:")
            click.echo(f"  • Min: ${self.df['hourly_price'].min():.2f}/hour")
            click.echo(f"  • Max: ${self.df['hourly_price'].max():.2f}/hour")
            click.echo(f"  • Average: ${self.df['hourly_price'].mean():.2f}/hour")
    
    def get_filter_options(self) -> Dict[str, List]:
        """Get available filter options from the dataset"""
        if self.df is None:
            return {}
        
        options = {}
        
        # Categorical filters
        categorical_cols = ['gpu_type', 'cloud', 'country', 'availability_region']
        for col in categorical_cols:
            if col in self.df.columns:
                options[col] = sorted(self.df[col].dropna().unique().tolist())
        
        # Numerical ranges
        numerical_cols = ['memory_in_gb', 'num_gpus', 'storage_in_gb', 'vram_per_gpu_in_gb', 'hourly_price', 'vcpus']
        for col in numerical_cols:
            if col in self.df.columns:
                options[f"{col}_range"] = [self.df[col].min(), self.df[col].max()]
        
        return options
    
    def apply_filters(self, filters: Dict[str, Any]) -> int:
        """Apply filters to the dataset and return number of results"""
        if self.df is None:
            return 0
        
        self.filtered_df = self.df.copy()
        
        # Apply each filter
        for key, value in filters.items():
            if value is None or value == '' or value == []:
                continue
                
            if key == 'available':
                self.filtered_df = self.filtered_df[self.filtered_df['available'] == value]
            elif key == 'gpu_type' and value in self.df['gpu_type'].values:
                self.filtered_df = self.filtered_df[self.filtered_df['gpu_type'] == value]
            elif key == 'cloud' and value in self.df['cloud'].values:
                self.filtered_df = self.filtered_df[self.filtered_df['cloud'] == value]
            elif key == 'country' and value in self.df['country'].values:
                self.filtered_df = self.filtered_df[self.filtered_df['country'] == value]
            elif key == 'availability_region' and value in self.df['availability_region'].values:
                self.filtered_df = self.filtered_df[self.filtered_df['availability_region'] == value]
            elif key == 'min_memory' and 'memory_in_gb' in self.df.columns:
                self.filtered_df = self.filtered_df[self.filtered_df['memory_in_gb'] >= value]
            elif key == 'min_storage' and 'storage_in_gb' in self.df.columns:
                self.filtered_df = self.filtered_df[self.filtered_df['storage_in_gb'] >= value]
            elif key == 'min_vcpus' and 'vcpus' in self.df.columns:
                self.filtered_df = self.filtered_df[self.filtered_df['vcpus'] >= value]
            elif key == 'min_vram' and 'vram_per_gpu_in_gb' in self.df.columns:
                self.filtered_df = self.filtered_df[self.filtered_df['vram_per_gpu_in_gb'] >= value]
            elif key == 'max_price' and 'hourly_price' in self.df.columns:
                self.filtered_df = self.filtered_df[self.filtered_df['hourly_price'] <= value]
            elif key == 'min_gpus' and 'num_gpus' in self.df.columns:
                self.filtered_df = self.filtered_df[self.filtered_df['num_gpus'] >= value]
        
        return len(self.filtered_df)
    
    def display_results(self, detailed: bool = False, limit: Optional[int] = None):
        """Display filtered results in a formatted table"""
        if self.filtered_df is None or len(self.filtered_df) == 0:
            click.echo("❌ No instances match your criteria.")
            return
        
        df_to_show = self.filtered_df.head(limit) if limit else self.filtered_df
        
        if detailed:
            # Show all important columns
            cols_to_show = [
                'cloud', 'shade_instance_type', 'gpu_type', 'num_gpus', 
                'memory_in_gb', 'storage_in_gb', 'vcpus', 'vram_per_gpu_in_gb', 
                'hourly_price', 'available', 'country', 'availability_region'
            ]
            # Only include columns that exist in the dataframe
            cols_to_show = [col for col in cols_to_show if col in df_to_show.columns]
        else:
            # Show compact view
            cols_to_show = ['cloud', 'gpu_type', 'num_gpus', 'hourly_price', 'available', 'country']
            cols_to_show = [col for col in cols_to_show if col in df_to_show.columns]
        
        # Format the display
        display_df = df_to_show[cols_to_show].copy()
        
        # Format price column if it exists
        if 'hourly_price' in display_df.columns:
            display_df['hourly_price'] = display_df['hourly_price'].apply(lambda x: f"${x:.2f}")
        
        # Format availability
        if 'available' in display_df.columns:
            display_df['available'] = display_df['available'].apply(lambda x: "✅ Yes" if x else "❌ No")
        
        click.echo(f"\n🔍 Found {len(self.filtered_df)} matching instances:")
        click.echo("="*80)
        click.echo(tabulate(display_df, headers='keys', tablefmt='grid', showindex=False))
        
        if limit and len(self.filtered_df) > limit:
            click.echo(f"\n... and {len(self.filtered_df) - limit} more instances")
    
    def export_results(self, filename: str, format: str = 'csv'):
        """Export filtered results to file"""
        if self.filtered_df is None or len(self.filtered_df) == 0:
            click.echo("❌ No data to export.")
            return
        
        try:
            # Convert hourly_price back to cents for export
            export_df = self.filtered_df.copy()
            if 'hourly_price' in export_df.columns:
                export_df['hourly_price'] = (export_df['hourly_price'] * 100).astype(int)
                
            if format.lower() == 'csv':
                export_df.to_csv(filename, index=False)
            elif format.lower() == 'json':
                export_df.to_json(filename, orient='records', indent=2)
            else:
                click.echo(f"❌ Unsupported format: {format}")
                return
            
            click.echo(f"✅ Exported {len(self.filtered_df)} instances to '{filename}'")
        except Exception as e:
            click.echo(f"❌ Error exporting data: {str(e)}")
    
    def interactive_mode(self):
        """Run interactive filtering mode"""
        if self.df is None:
            click.echo("❌ No data loaded. Please load a CSV file first.")
            return
        
        click.echo("\n🚀 GPU Provider Selector - Interactive Mode")
        click.echo("="*50)
        click.echo("Available commands:")
        click.echo("  summary    - Show dataset summary")
        click.echo("  filter     - Apply filters interactively")
        click.echo("  show       - Display current results")
        click.echo("  export     - Export results to file")
        click.echo("  clear      - Clear all filters")
        click.echo("  quit       - Exit interactive mode")
        click.echo("="*50)
        
        while True:
            try:
                command = click.prompt("\n>", type=str).strip().lower()
                
                if command == 'quit' or command == 'q':
                    break
                elif command == 'summary':
                    self.show_summary()
                elif command == 'show':
                    limit = click.prompt("How many results to show? (Enter for all)", default="", type=str).strip()
                    limit = int(limit) if limit.isdigit() else None
                    detailed = click.confirm("Show detailed view?", default=False)
                    self.display_results(detailed=detailed, limit=limit)
                elif command == 'filter':
                    self.interactive_filter()
                elif command == 'export':
                    filename = click.prompt("Enter filename", type=str).strip()
                    format_type = click.prompt("Format (csv/json)", default="csv", type=str).strip()
                    self.export_results(filename, format_type)
                elif command == 'clear':
                    self.filtered_df = self.df.copy()
                    click.echo("✅ All filters cleared!")
                else:
                    click.echo("❌ Unknown command. Try: summary, filter, show, export, clear, quit")
            
            except KeyboardInterrupt:
                click.echo("\n👋 Goodbye!")
                break
            except Exception as e:
                click.echo(f"❌ Error: {str(e)}")
    
    def interactive_filter(self):
        """Interactive filter application"""
        filters = {}
        options = self.get_filter_options()
        
        click.echo("\n🔧 Interactive Filtering")
        click.echo("-" * 30)
        
        # Availability filter
        avail = click.prompt("Show only available instances? (y/n/any)", default="any", type=str).strip().lower()
        if avail == 'y':
            filters['available'] = True
        elif avail == 'n':
            filters['available'] = False
        
        # GPU Type filter
        if 'gpu_type' in options:
            click.echo(f"\nAvailable GPU types: {', '.join(options['gpu_type'])}")
            gpu_type = click.prompt("GPU type (or Enter for any)", default="", type=str).strip()
            if gpu_type and gpu_type in options['gpu_type']:
                filters['gpu_type'] = gpu_type
        
        # Cloud filter
        if 'cloud' in options:
            click.echo(f"\nAvailable clouds: {', '.join(options['cloud'])}")
            cloud = click.prompt("Cloud (or Enter for any)", default="", type=str).strip()
            if cloud and cloud in options['cloud']:
                filters['cloud'] = cloud
        
        # Country filter
        if 'country' in options:
            click.echo(f"\nAvailable countries: {', '.join(options['country'])}")
            country = click.prompt("Country (or Enter for any)", default="", type=str).strip()
            if country and country in options['country']:
                filters['country'] = country
        
        # Availability Region filter
        if 'availability_region' in options:
            click.echo(f"\nAvailable regions: {', '.join(options['availability_region'])}")
            region = click.prompt("Availability region (or Enter for any)", default="", type=str).strip()
            if region and region in options['availability_region']:
                filters['availability_region'] = region
        
        # Price filter
        if 'hourly_price_range' in options:
            price_range = options['hourly_price_range']
            click.echo(f"\nPrice range: ${price_range[0]:.2f} - ${price_range[1]:.2f}")
            max_price = click.prompt("Maximum price per hour (or Enter for any)", default="", type=str).strip()
            if max_price and max_price.replace('.', '').isdigit():
                filters['max_price'] = float(max_price)
        
        # Memory filter
        if 'memory_in_gb_range' in options:
            mem_range = options['memory_in_gb_range']
            click.echo(f"\nMemory range: {mem_range[0]}GB - {mem_range[1]}GB")
            min_memory = click.prompt("Minimum memory (GB) (or Enter for any)", default="", type=str).strip()
            if min_memory and min_memory.isdigit():
                filters['min_memory'] = int(min_memory)
        
        # Storage filter
        if 'storage_in_gb_range' in options:
            stor_range = options['storage_in_gb_range']
            click.echo(f"\nStorage range: {stor_range[0]}GB - {stor_range[1]}GB")
            min_storage = click.prompt("Minimum storage (GB) (or Enter for any)", default="", type=str).strip()
            if min_storage and min_storage.isdigit():
                filters['min_storage'] = int(min_storage)
        
        # GPU count filter
        if 'num_gpus_range' in options:
            gpu_range = options['num_gpus_range']
            click.echo(f"\nGPU count range: {gpu_range[0]} - {gpu_range[1]}")
            min_gpus = click.prompt("Minimum GPUs (or Enter for any)", default="", type=str).strip()
            if min_gpus and min_gpus.isdigit():
                filters['min_gpus'] = int(min_gpus)
        
        # VRAM filter
        if 'vram_per_gpu_in_gb_range' in options:
            vram_range = options['vram_per_gpu_in_gb_range']
            click.echo(f"\nVRAM per GPU range: {vram_range[0]}GB - {vram_range[1]}GB")
            min_vram = click.prompt("Minimum VRAM per GPU (GB) (or Enter for any)", default="", type=str).strip()
            if min_vram and min_vram.isdigit():
                filters['min_vram'] = int(min_vram)
        
        # vCPUs filter
        if 'vcpus_range' in options:
            vcpu_range = options['vcpus_range']
            click.echo(f"\nvCPUs range: {vcpu_range[0]} - {vcpu_range[1]}")
            min_vcpus = click.prompt("Minimum vCPUs (or Enter for any)", default="", type=str).strip()
            if min_vcpus and min_vcpus.isdigit():
                filters['min_vcpus'] = int(min_vcpus)
        
        # Apply filters
        result_count = self.apply_filters(filters)
        click.echo(f"\n✅ Filters applied! Found {result_count} matching instances.")
        
        if result_count > 0:
            show_results = click.confirm("Show results now?", default=True)
            if show_results:
                self.display_results(limit=10)

@click.command()
@click.option('--csv', type=click.Path(exists=True), help='Path to CSV file')
@click.option('--sample', is_flag=True, help='Use sample data')
@click.option('--interactive', '-i', is_flag=True, help='Run in interactive mode')
@click.option('--summary', is_flag=True, help='Show dataset summary')
@click.option('--available', type=bool, help='Filter by availability')
@click.option('--gpu-type', type=str, help='Filter by GPU type')
@click.option('--cloud', type=str, help='Filter by cloud provider')
@click.option('--country', type=str, help='Filter by country')
@click.option('--availability-region', type=str, help='Filter by availability region')
@click.option('--max-price', type=float, help='Maximum price per hour (in dollars)')
@click.option('--min-memory', type=int, help='Minimum memory in GB')
@click.option('--min-storage', type=int, help='Minimum storage in GB')
@click.option('--min-gpus', type=int, help='Minimum number of GPUs')
@click.option('--min-vram', type=int, help='Minimum VRAM per GPU in GB')
@click.option('--min-vcpus', type=int, help='Minimum number of vCPUs')
@click.option('--limit', type=int, help='Limit number of results shown')
@click.option('--detailed', is_flag=True, help='Show detailed results')
@click.option('--export', type=str, help='Export results to file')
@click.option('--format', type=click.Choice(['csv', 'json'], case_sensitive=False), default='csv', help='Export format')
def main(csv, sample, interactive, summary, available, gpu_type, cloud, country, 
         availability_region, max_price, min_memory, min_storage, min_gpus, min_vram, 
         min_vcpus, limit, detailed, export, format):
    """GPU Provider Selector"""
    # Initialize selector
    selector = GPUProviderSelector()
    
    # Load data
    if sample:
        selector.load_sample_data()
    elif csv:
        if not selector.load_csv(csv):
            sys.exit(1)
    else:
        click.echo("🚀 GPU Provider Selector")
        click.echo("=" * 40)
        click.echo("Choose an option:")
        click.echo("1. Load CSV file")
        click.echo("2. Use sample data")
        
        choice = click.prompt("Enter choice (1/2)", type=str).strip()
        
        if choice == '1':
            file_path = click.prompt("Enter CSV file path", type=str).strip()
            if not selector.load_csv(file_path):
                sys.exit(1)
        elif choice == '2':
            selector.load_sample_data()
        else:
            click.echo("❌ Invalid choice.")
            sys.exit(1)
    
    # Show summary if requested
    if summary:
        selector.show_summary()
    
    # Run interactive mode
    if interactive:
        selector.interactive_mode()
        sys.exit(0)
    
    # Apply command-line filters
    filters = {}
    if available is not None:
        filters['available'] = available
    if gpu_type:
        filters['gpu_type'] = gpu_type
    if cloud:
        filters['cloud'] = cloud
    if country:
        filters['country'] = country
    if availability_region:
        filters['availability_region'] = availability_region
    if max_price is not None:
        filters['max_price'] = max_price
    if min_memory is not None:
        filters['min_memory'] = min_memory
    if min_storage is not None:
        filters['min_storage'] = min_storage
    if min_gpus is not None:
        filters['min_gpus'] = min_gpus
    if min_vram is not None:
        filters['min_vram'] = min_vram
    if min_vcpus is not None:
        filters['min_vcpus'] = min_vcpus
    
    # Apply filters and show results
    if filters:
        result_count = selector.apply_filters(filters)
        click.echo(f"\n✅ Applied filters. Found {result_count} matching instances.")
    
    # Display results
    selector.display_results(detailed=detailed, limit=limit)
    
    # Export if requested
    if export:
        selector.export_results(export, format)
    
    sys.exit(0)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        click.echo("\n👋 Goodbye!")
        sys.exit(0)