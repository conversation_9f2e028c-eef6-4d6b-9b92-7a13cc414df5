import os
import requests
import json
from collections import defaultdict
from pydantic import ValidationError
from dotenv import load_dotenv

from gpu_on_demand_manager.models.shadeform.instances_types import (
    APIResponse as InstancesTypesAPIResponse,
)

load_dotenv()
API_KEY = os.getenv("SHADEFORM_API_KEY")


class ShadeformSDK:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.default_headers = {"X-API-KEY": f"{api_key}"}

    def check_availability(self):
        url = "https://api.shadeform.ai/v1/instances/types"
        response = requests.request(
            "GET",
            url,
            headers=self.default_headers,
            params={
                "availability": True,
                "num_gpus": 4,
                "sort": "price",
            },
        )

        if response.status_code == 200:
            try:
                data = response.json()  # Convert response to dictionary
                parsed_response = InstancesTypesAPIResponse.model_validate(
                    data
                )  # Validate and parse data

                # Group instances by country using display_name field
                countries_data = defaultdict(set)  # Use set to avoid duplicate instances
                for instance_type in parsed_response.instance_types:
                    for availability_info in instance_type.availability:
                        display_name = availability_info.display_name
                        # Extract country from display_name (e.g., "SG, Singapore" -> "SG")
                        if "," in display_name:
                            country_code = display_name.split(',')[0].strip().lower()
                            if country_code:  # Ensure country code is not empty
                                # Use a unique identifier to avoid duplicates
                                instance_key = (instance_type.shade_instance_type, availability_info.region)
                                countries_data[country_code].add(json.dumps(instance_key))

                # Convert sets back to lists of filtered instances for JSON output
                countries_instances = defaultdict(list)
                for instance_type in parsed_response.instance_types:
                    for availability_info in instance_type.availability:
                        display_name = availability_info.display_name
                        if "," in display_name:
                            country_code = display_name.split(',')[0].strip().lower()
                            if country_code:
                                instance_key = (instance_type.shade_instance_type, availability_info.region)
                                if json.dumps(instance_key) in countries_data[country_code]:
                                    # Create a copy of the instance with filtered availability
                                    instance_dict = instance_type.model_dump()
                                    # Keep only the availability entry matching the country
                                    instance_dict['availability'] = [
                                        avail for avail in instance_dict['availability']
                                        if avail['display_name'].split(',')[0].strip().lower() == country_code
                                    ]
                                    # Only append if there are matching availability entries
                                    if instance_dict['availability']:
                                        countries_instances[country_code].append(instance_dict)

                # Create 'gpu_data_by_country' directory if it doesn't exist
                output_dir = "gpu_data_by_country"
                os.makedirs(output_dir, exist_ok=True)

                # Write data to separate JSON files for each country
                for country, instances in countries_instances.items():
                    file_path = os.path.join(output_dir, f"{country}.json")
                    with open(file_path, "w") as f:
                        json.dump(list(instances), f, indent=2)
                    print(f"Created {file_path} with GPU information for country {country}")

            except ValidationError as e:
                print("Validation Error:", e)
            except Exception as e:
                print(f"An unexpected error occurred: {e}")
        else:
            print("Failed to fetch data:", response.status_code, response.text)


if __name__ == "__main__":
    if API_KEY is None:
        print("Error: SHADEFORM_API_KEY not found in .env file.")
    else:
        sdk = ShadeformSDK(api_key=API_KEY)
        sdk.check_availability()