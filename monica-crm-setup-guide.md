# Monica CRM Automation Setup Guide

This guide will help you set up the n8n automation for Monica CRM meeting notes processing.

## Overview

The automation performs the following tasks every 5 minutes:
1. Fetches all contacts from Monica CRM
2. Retrieves notes for each contact that were updated today
3. Uses an LLM to analyze and categorize the notes into Summary, Tasks, Email, and Reminder&Calls
4. Creates call logs with summaries
5. Sends email summaries to yourself
6. Creates tasks in Monica CRM
7. Creates reminders for calls and meetings

## Prerequisites

1. **Monica CRM Instance**: You need a running Monica CRM instance with API access
2. **n8n Instance**: A running n8n instance (self-hosted or cloud)
3. **OpenAI API Key**: For the LLM analysis (or alternative LLM provider)
4. **Email SMTP**: For sending summary emails

## Setup Instructions

### 1. Import the Workflow

1. Copy the contents of `monica-crm-automation.json`
2. In n8n, go to **Workflows** → **Import from File**
3. Paste the JSON content and import

### 2. Configure Environment Variables

In n8n, set up the following environment variables:

```bash
MONICA_BASE_URL=https://your-monica-instance.com
YOUR_EMAIL=<EMAIL>
```

### 3. Set Up Credentials

#### Monica API Key
1. In Monica CRM, go to **Settings** → **API**
2. Generate a new API token
3. In n8n, create a new credential of type **HTTP Header Auth**
4. Set:
   - **Name**: `monica-api-key`
   - **Header Name**: `Authorization`
   - **Header Value**: `Bearer YOUR_MONICA_API_TOKEN`

#### OpenAI API Key
1. Get your OpenAI API key from https://platform.openai.com/api-keys
2. In n8n, create a new credential of type **OpenAI**
3. Set:
   - **Name**: `openai-api-key`
   - **API Key**: Your OpenAI API key

#### SMTP Credentials
1. In n8n, create a new credential of type **SMTP**
2. Set:
   - **Name**: `smtp-credentials`
   - **Host**: Your SMTP server (e.g., smtp.gmail.com)
   - **Port**: 587 (or your SMTP port)
   - **Security**: STARTTLS
   - **Username**: Your email username
   - **Password**: Your email password/app password

### 4. Monica CRM API Endpoints

The automation uses these Monica CRM API endpoints:
- `GET /api/contacts` - Fetch all contacts
- `GET /api/contacts/{id}/notes` - Get notes for a contact
- `POST /api/contacts/{id}/calls` - Create call logs
- `POST /api/contacts/{id}/tasks` - Create tasks
- `POST /api/contacts/{id}/reminders` - Create reminders

### 5. Testing the Workflow

1. **Manual Test**: Click "Execute Workflow" to test manually
2. **Add Test Notes**: Add some notes to a contact in Monica CRM with today's date
3. **Check Results**: Verify that:
   - Call logs are created
   - Tasks are added
   - Reminders are set
   - Email summary is sent

## Workflow Details

### Node Descriptions

1. **Every 5 Minutes**: Cron trigger that runs the workflow every 5 minutes
2. **Get All Contacts**: Fetches all contacts from Monica CRM
3. **Process Contacts List**: Prepares contact data for processing
4. **Get Contact Notes**: Retrieves notes for each contact
5. **Filter Today's Notes**: Filters notes updated today to minimize LLM costs
6. **Analyze Notes with LLM**: Uses OpenAI to categorize notes
7. **Parse LLM Response**: Processes the LLM JSON response
8. **Create Call Log**: Adds summary to call logs
9. **Send Summary Email**: Emails summary to yourself
10. **Split Tasks**: Prepares tasks for batch creation
11. **Create Tasks**: Adds tasks to Monica CRM
12. **Split Reminders**: Prepares reminders for batch creation
13. **Create Reminders**: Adds reminders to Monica CRM

### LLM System Prompt

The LLM uses this system prompt to analyze meeting notes:

```
# Role (R)
You are an AI assistant skilled in summarizing meeting notes with a focus on client interactions and follow-up actions.

# Task (T)
Your task is to analyze the provided meeting notes and generate a **structured json** summary divided into four sections: **Summary**, **Tasks**, **Email**, and **Reminder&Calls**.

# Instructions (I)
- **Summary:** Provide a concise summary of all topics discussed with the client(s), only including relevant client-related content.
- **Tasks:** List all actionable items or ToDos derived from the meeting, excluding any instructions related to contacting people via call or email.
- **Email:** Extract details about emailing individuals mentioned in the notes, including personal information such as first name, last name, and any other identifying details relevant to the email recipient.
- **Reminder&Calls:** Identify people who need to be contacted by phone or scheduled for meetings, specifying who to contact, the reason for the call or meeting, and any pertinent details.
- If some information is not found, keep that **part empty**.
```

## Troubleshooting

### Common Issues

1. **API Authentication Errors**
   - Verify Monica API token is correct
   - Check that the token has proper permissions

2. **LLM Parsing Errors**
   - The LLM response must be valid JSON
   - Check OpenAI API key and quota

3. **Email Sending Issues**
   - Verify SMTP credentials
   - Check firewall/security settings

4. **No Notes Processed**
   - Ensure notes were updated today
   - Check date filtering logic

### Monitoring

- Check n8n execution logs for errors
- Monitor OpenAI API usage to control costs
- Verify Monica CRM data is being created correctly

## Customization

### Changing LLM Provider
Replace the OpenAI node with your preferred LLM provider (Anthropic, local models, etc.)

### Modifying Schedule
Change the cron trigger interval in the "Every 5 Minutes" node

### Email Template
Customize the email HTML template in the "Send Summary Email" node

### Reminder Timing
Modify the reminder date calculation in the "Split Reminders" node (currently set to 7 days)

## Security Considerations

1. **API Keys**: Store all API keys securely in n8n credentials
2. **Network**: Ensure secure connections to all services
3. **Data**: Be mindful of sensitive client information in notes
4. **Access**: Limit n8n access to authorized users only

## Support

For issues with:
- **Monica CRM**: Check Monica documentation and community
- **n8n**: Refer to n8n documentation and forums
- **OpenAI**: Check OpenAI status and documentation
