from typing import List
from pydantic import BaseModel


class Configuration(BaseModel):
    memory_in_gb: int
    storage_in_gb: int
    vcpus: int
    num_gpus: int
    gpu_type: str
    interconnect: str
    vram_per_gpu_in_gb: int
    os_options: List[str]


class Availability(BaseModel):
    region: str
    available: bool
    display_name: str


class BootTime(BaseModel):
    min_boot_in_sec: int
    max_boot_in_sec: int


class InstanceType(BaseModel):
    cloud: str
    shade_instance_type: str
    cloud_instance_type: str
    memory_in_gb: int
    storage_in_gb: int
    vcpus: int
    num_gpus: int
    gpu_type: str
    interconnect: str
    nvlink: bool
    configuration: Configuration
    hourly_price: float
    deployment_type: str
    availability: List[Availability]
    boot_time: BootTime


class APIResponse(BaseModel):
    instance_types: List[InstanceType]
