# Import necessary libraries and modules
import requests
import os
import pandas as pd
import time
from datetime import datetime
from dotenv import load_dotenv
import click

# Load environment variables from a .env file
load_dotenv()

# Retrieve API keys from environment variables
API_KEY = os.getenv("SHADEFORM_API_KEY")
SFORM_PERF_KEY = os.getenv('SFORM_PERF_KEY')

# Define the common header for API requests
HEADER = {
    "X-API-KEY": f"{API_KEY}"
}

def delete_instance(instance_id):
    # Construct the URL for the instance deletion request
    url = f"https://api.shadeform.ai/v1/instances/{instance_id}/delete"
    # Send the request to delete the instance
    response = requests.post(url, headers=HEADER)
    # Raise an HTTPError for bad responses (4xx or 5xx)
    response.raise_for_status()
    print(f"Instance {instance_id} deleted successfully.") # Confirmation message
    return response.json() # Return response for further handling if needed

def create_instance(row_data, row_number):
    # Construct the URL for the instance creation request
    create_url = "https://api.shadeform.ai/v1/instances/create"

    # Construct the payload for the instance creation request
    payload = {
        "cloud": row_data['cloud'],
        "name": f"shadeform-test-{datetime.now().strftime('%Y%m%d_%H%M%S')}", # Unique name based on row number
        "os": row_data['os_option'],
        "shade_cloud": True,
        "shade_instance_type": row_data['shade_instance_type'],
        "region": row_data['region'],
        "ssh_key_id": f"{SFORM_PERF_KEY}"
    }
    # Send the request to create the instance
    response = requests.post(create_url, json=payload, headers=HEADER)
    # Raise an HTTPError for bad responses (4xx or 5xx)
    response.raise_for_status()
    return response.json()

def get_instance_info(instance_id):
    """Get instance information"""
    url = f"https://api.shadeform.ai/v1/instances/{instance_id}/info"
    response = requests.get(url, headers=HEADER)
    response.raise_for_status()
    return response.json()

def wait_for_instance_active(instance_id, max_wait_minutes=30):
    """Wait for instance to become active and return timing info"""
    print(f"Waiting for instance {instance_id} to become active...")
    start_time = time.time()
    max_wait_seconds = max_wait_minutes * 60

    while time.time() - start_time < max_wait_seconds:
        try:
            info = get_instance_info(instance_id)
            print(f"Instance {instance_id} status: {info.get('status', 'unknown')}")

            if info.get('active_at') is not None:
                print(f"Instance {instance_id} is now active!")
                return info

        except Exception as e:
            print(f"Error checking instance {instance_id}: {e}")

        time.sleep(30)  # Wait 30 seconds between checks

    raise TimeoutError(f"Instance {instance_id} did not become active within {max_wait_minutes} minutes")

def calculate_time_difference(created_at_str, active_at_str):
    """Calculate difference between created_at and active_at in seconds"""
    try:
        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
        active_at = datetime.fromisoformat(active_at_str.replace('Z', '+00:00'))
        return int((active_at - created_at).total_seconds())
    except Exception as e:
        print(f"Error calculating time difference: {e}")
        return None

def print_ssh_info(instance_info):
    """Print SSH connection information"""
    print("\n" + "="*50)
    print("SSH CONNECTION INFORMATION")
    print("="*50)
    print(f"Instance ID: {instance_info.get('id', 'N/A')}")
    print(f"Instance Name: {instance_info.get('name', 'N/A')}")
    print(f"Status: {instance_info.get('status', 'N/A')}")
    print(f"IP Address: {instance_info.get('ip', 'N/A')}")
    print(f"SSH User: {instance_info.get('ssh_user', 'N/A')}")
    print(f"SSH Port: {instance_info.get('ssh_port', 'N/A')}")

    # Calculate activation time
    created_at = instance_info.get('created_at')
    active_at = instance_info.get('active_at')
    if created_at and active_at:
        activation_time = calculate_time_difference(created_at, active_at)
        print(f"Activation Time: {activation_time} seconds")

    print(f"Cost Estimate: ${instance_info.get('cost_estimate', 'N/A')}")
    print(f"Hourly Price: ${instance_info.get('hourly_price', 'N/A')}")

    # SSH command
    ip = instance_info.get('ip')
    ssh_user = instance_info.get('ssh_user')
    ssh_port = instance_info.get('ssh_port', 22)

    if ip and ssh_user:
        print(f"\nSSH Command:")
        print(f"ssh -i ~/.ssh/sform_key {ssh_user}@{ip} -p {ssh_port}")

    print("="*50)

@click.command() # Decorator to make this function a Click command
@click.argument('rn', type=int) # Decorator for the 'rn' argument
@click.option('--csv-file', default='nvidia-smi_test.csv', help='CSV file to read instance data from')
def main(rn, csv_file):
    try:
        # Read the CSV file into a pandas DataFrame
        df = pd.read_csv(csv_file, index_col=False)

        # Get the row data (using rn - 1 because pandas is 0-indexed)
        if 0 <= (rn - 1) < len(df):
            row_data = df.iloc[rn - 1]

            print(f"Creating instance from row {rn}...")
            print(f"Instance type: {row_data.get('shade_instance_type', 'N/A')}")
            print(f"Region: {row_data.get('region', 'N/A')}")
            print(f"Cloud: {row_data.get('cloud', 'N/A')}")

            # Call the create_instance function with the extracted row data and row number
            instance_details = create_instance(row_data, rn)
            instance_id = instance_details.get('id')

            print("Instance creation successful!")
            print(f"Instance ID: {instance_id}")

            # Wait for instance to become active
            print("\nWaiting for instance to become active...")
            active_info = wait_for_instance_active(instance_id)

            # Print SSH connection information
            print_ssh_info(active_info)

        else:
            print(f"Error: Row number {rn} is out of bounds for the CSV file.")
            print(f"Please provide a row number between 1 and {len(df)}.")

    except FileNotFoundError:
        print(f"Error: '{csv_file}' not found. Please ensure it's in the same directory.")
    except requests.exceptions.RequestException as e:
        print(f"API Error: {e}")
        if e.response is not None:
            print(f"Response content: {e.response.text}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    main() 