[{"cloud": "vultr", "shade_instance_type": "A16x4", "cloud_instance_type": "vcg-a16-24c-256g-64vram", "memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 205.0, "deployment_type": "vm", "availability": [{"region": "tokyo-japan-2", "available": true, "display_name": "JP, Tokyo"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A40x4", "cloud_instance_type": "vcg-a40-96c-480g-192vram", "memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 744.0, "deployment_type": "vm", "availability": [{"region": "tokyo-japan-2", "available": false, "display_name": "JP, Tokyo"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}]