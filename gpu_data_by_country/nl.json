[{"cloud": "paperspace", "shade_instance_type": "A4000x4", "cloud_instance_type": "A4000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "amsterdam-netherlands-1", "available": false, "display_name": "NL, Amsterdam"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A5000x4", "cloud_instance_type": "A5000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 568.0, "deployment_type": "vm", "availability": [{"region": "amsterdam-netherlands-1", "available": false, "display_name": "NL, Amsterdam"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A6000x4", "cloud_instance_type": "A6000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 772.0, "deployment_type": "vm", "availability": [{"region": "amsterdam-netherlands-1", "available": false, "display_name": "NL, Amsterdam"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "V100_32Gx4", "cloud_instance_type": "V100-32Gx4", "memory_in_gb": 120, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "V100_32G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 120, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "V100_32G", "interconnect": "pcie", "vram_per_gpu_in_gb": 32, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 936.0, "deployment_type": "vm", "availability": [{"region": "amsterdam-netherlands-1", "available": false, "display_name": "NL, Amsterdam"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A100x4", "cloud_instance_type": "A100x4", "memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 1244.0, "deployment_type": "vm", "availability": [{"region": "amsterdam-netherlands-1", "available": false, "display_name": "NL, Amsterdam"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A100_sxm4_80Gx4", "cloud_instance_type": "A100-80Gx4", "memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 1288.0, "deployment_type": "vm", "availability": [{"region": "amsterdam-netherlands-1", "available": false, "display_name": "NL, Amsterdam"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}]