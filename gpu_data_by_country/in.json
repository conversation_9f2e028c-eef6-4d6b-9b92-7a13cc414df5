[{"cloud": "vultr", "shade_instance_type": "A16x4", "cloud_instance_type": "vcg-a16-24c-256g-64vram", "memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 205.0, "deployment_type": "vm", "availability": [{"region": "bangalore-india-1", "available": true, "display_name": "IN, Bangalore"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "mumbai-india-1", "available": false, "display_name": "IN, Mumbai"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "mumbai-india-1", "available": false, "display_name": "IN, Mumbai"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A40x4", "cloud_instance_type": "vcg-a40-96c-480g-192vram", "memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 744.0, "deployment_type": "vm", "availability": [{"region": "bangalore-india-1", "available": false, "display_name": "IN, Bangalore"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}]