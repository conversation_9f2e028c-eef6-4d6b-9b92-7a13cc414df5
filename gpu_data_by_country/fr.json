[{"cloud": "scaleway", "shade_instance_type": "L4x4", "cloud_instance_type": "L4-4-24G", "memory_in_gb": 192, "storage_in_gb": 500, "vcpus": 32, "num_gpus": 4, "gpu_type": "L4", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 500, "vcpus": 32, "num_gpus": 4, "gpu_type": "L4", "interconnect": "pcie", "vram_per_gpu_in_gb": 0, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 337.0, "deployment_type": "vm", "availability": [{"region": "paris-france-1", "available": true, "display_name": "FR, Paris"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "scaleway", "shade_instance_type": "L40Sx4", "cloud_instance_type": "L40S-4-48G", "memory_in_gb": 384, "storage_in_gb": 6900, "vcpus": 32, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 384, "storage_in_gb": 6900, "vcpus": 32, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 623.0, "deployment_type": "vm", "availability": [{"region": "paris-france-1", "available": true, "display_name": "FR, Paris"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}]