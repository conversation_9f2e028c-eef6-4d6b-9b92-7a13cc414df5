[{"cloud": "hyperstack", "shade_instance_type": "A4000x4", "cloud_instance_type": "n3-RTX-A4000x4", "memory_in_gb": 86, "storage_in_gb": 500, "vcpus": 16, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 86, "storage_in_gb": 500, "vcpus": 16, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 60.0, "deployment_type": "vm", "availability": [{"region": "oslo-norway-1", "available": false, "display_name": "NO, Oslo"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "hyperstack", "shade_instance_type": "A5000x4", "cloud_instance_type": "n2-RTX-A5000x4", "memory_in_gb": 120, "storage_in_gb": 800, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 120, "storage_in_gb": 800, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 100.0, "deployment_type": "vm", "availability": [{"region": "oslo-norway-1", "available": false, "display_name": "NO, Oslo"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "hyperstack", "shade_instance_type": "A6000x4", "cloud_instance_type": "n3-RTX-A6000x4", "memory_in_gb": 232, "storage_in_gb": 750, "vcpus": 124, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 232, "storage_in_gb": 750, "vcpus": 124, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 200.0, "deployment_type": "vm", "availability": [{"region": "oslo-norway-1", "available": false, "display_name": "NO, Oslo"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "excesssupply", "shade_instance_type": "RTX4090x4", "cloud_instance_type": "n3-RTX-4090x4", "memory_in_gb": 352, "storage_in_gb": 3100, "vcpus": 60, "num_gpus": 4, "gpu_type": "RTX4090", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 352, "storage_in_gb": 3100, "vcpus": 60, "num_gpus": 4, "gpu_type": "RTX4090", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 240.0, "deployment_type": "vm", "availability": [{"region": "oslo-norway-3", "available": true, "display_name": "NO, Central"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 1200}}, {"cloud": "hyperstack", "shade_instance_type": "RTX6000Adax4", "cloud_instance_type": "n1-RTX-A6000-ADAx4", "memory_in_gb": 238, "storage_in_gb": 1700, "vcpus": 64, "num_gpus": 4, "gpu_type": "RTX6000Ada", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 238, "storage_in_gb": 1700, "vcpus": 64, "num_gpus": 4, "gpu_type": "RTX6000Ada", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 440.0, "deployment_type": "vm", "availability": [{"region": "oslo-norway-1", "available": false, "display_name": "NO, Oslo"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "hyperstack", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "n3-A100x4", "memory_in_gb": 480, "storage_in_gb": 3300, "vcpus": 124, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 3300, "vcpus": 124, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 540.0, "deployment_type": "vm", "availability": [{"region": "oslo-norway-1", "available": false, "display_name": "NO, Oslo"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "cudo", "shade_instance_type": "L40Sx4", "cloud_instance_type": "epyc-genoa-l40s-compute", "memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 591.0, "deployment_type": "vm", "availability": [{"region": "kristiansand-norway-2", "available": true, "display_name": "NO, Kristiansand"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}, {"cloud": "cudo", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "epyc-genoa-a100-pcie", "memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 732.0, "deployment_type": "vm", "availability": [{"region": "kristiansand-norway-2", "available": false, "display_name": "NO, Kristiansand"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}, {"cloud": "cudo", "shade_instance_type": "H100x4", "cloud_instance_type": "sapphire-rapids-h100", "memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 1142.0, "deployment_type": "vm", "availability": [{"region": "kristiansand-norway-2", "available": false, "display_name": "NO, Kristiansand"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}]