[{"cloud": "datacrunch", "shade_instance_type": "V100x4", "cloud_instance_type": "4V100.20V", "memory_in_gb": 90, "storage_in_gb": 1000, "vcpus": 20, "num_gpus": 4, "gpu_type": "V100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 90, "storage_in_gb": 1000, "vcpus": 20, "num_gpus": 4, "gpu_type": "V100", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 156.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-1", "available": true, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "RTX6000Adax4", "cloud_instance_type": "4RTX6000ADA.40V", "memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 40, "num_gpus": 4, "gpu_type": "RTX6000Ada", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 40, "num_gpus": 4, "gpu_type": "RTX6000Ada", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 428.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-1", "available": true, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "A6000x4", "cloud_instance_type": "4A6000.40V", "memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 40, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 40, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 432.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-1", "available": true, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "A100_sxm4x4", "cloud_instance_type": "4A100.40S.88V", "memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100", "interconnect": "sxm4", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 544.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": false, "display_name": "FI, Central"}, {"region": "helsinki-finland-1", "available": false, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "A100_sxm4x4", "cloud_instance_type": "4A100.40S.88V", "memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100", "interconnect": "sxm4", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 544.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": false, "display_name": "FI, Central"}, {"region": "helsinki-finland-1", "available": false, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "L40Sx4", "cloud_instance_type": "4L40S.80V", "memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 80, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 80, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 544.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": false, "display_name": "FI, Central"}, {"region": "helsinki-finland-1", "available": false, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "L40Sx4", "cloud_instance_type": "4L40S.80V", "memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 80, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 240, "storage_in_gb": 1000, "vcpus": 80, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 544.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": false, "display_name": "FI, Central"}, {"region": "helsinki-finland-1", "available": false, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "A100_sxm4_80Gx4", "cloud_instance_type": "4A100.88V", "memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 728.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": true, "display_name": "FI, Central"}, {"region": "helsinki-finland-1", "available": true, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "A100_sxm4_80Gx4", "cloud_instance_type": "4A100.88V", "memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 88, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 728.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": true, "display_name": "FI, Central"}, {"region": "helsinki-finland-1", "available": true, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "H100_sxm5x4", "cloud_instance_type": "4H100.80S.120V", "memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 120, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "nvlink": true, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1000, "vcpus": 120, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 904.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-1", "available": false, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "H200x4", "cloud_instance_type": "4H200.141S.176V", "memory_in_gb": 740, "storage_in_gb": 1000, "vcpus": 176, "num_gpus": 4, "gpu_type": "H200", "interconnect": "SXM5", "nvlink": true, "configuration": {"memory_in_gb": 740, "storage_in_gb": 1000, "vcpus": 176, "num_gpus": 4, "gpu_type": "H200", "interconnect": "SXM5", "vram_per_gpu_in_gb": 141, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu20.04_cuda12.4_shade_os", "ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 1348.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-2", "available": true, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "datacrunch", "shade_instance_type": "B200x4", "cloud_instance_type": "4B200.124V", "memory_in_gb": 1000, "storage_in_gb": 250, "vcpus": 124, "num_gpus": 4, "gpu_type": "B200", "interconnect": "sxm6", "nvlink": true, "configuration": {"memory_in_gb": 1000, "storage_in_gb": 250, "vcpus": 124, "num_gpus": 4, "gpu_type": "B200", "interconnect": "sxm6", "vram_per_gpu_in_gb": 192, "os_options": ["ubuntu24.04_cuda12.8_shade_os"]}, "hourly_price": 1960.0, "deployment_type": "vm", "availability": [{"region": "helsinki-finland-5", "available": false, "display_name": "FI, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}]