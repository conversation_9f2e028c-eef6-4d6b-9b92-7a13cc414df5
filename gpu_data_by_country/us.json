[{"cloud": "massedcompute", "shade_instance_type": "A30x4", "cloud_instance_type": "gpu_4x_A30", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 50, "num_gpus": 4, "gpu_type": "A30", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 50, "num_gpus": 4, "gpu_type": "A30", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 132.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A30x4", "cloud_instance_type": "gpu_4x_A30", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 50, "num_gpus": 4, "gpu_type": "A30", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 50, "num_gpus": 4, "gpu_type": "A30", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 132.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A5000x4", "cloud_instance_type": "gpu_4x_a5000", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 40, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 40, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 164.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "imwt", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 196.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-2", "available": false, "display_name": "US, Central"}, {"region": "kansascity-usa-2", "available": true, "display_name": "US, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "imwt", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 196.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-2", "available": false, "display_name": "US, Central"}, {"region": "kansascity-usa-2", "available": true, "display_name": "US, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A16x4", "cloud_instance_type": "vcg-a16-24c-256g-64vram", "memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 205.0, "deployment_type": "vm", "availability": [{"region": "sanjose-usa-1", "available": true, "display_name": "US, Silicon Valley, CA"}, {"region": "chicago-usa-1", "available": true, "display_name": "US, Chicago, IL"}, {"region": "newark-usa-1", "available": true, "display_name": "US, Newark, NJ"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A16x4", "cloud_instance_type": "vcg-a16-24c-256g-64vram", "memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 205.0, "deployment_type": "vm", "availability": [{"region": "sanjose-usa-1", "available": true, "display_name": "US, Silicon Valley, CA"}, {"region": "chicago-usa-1", "available": true, "display_name": "US, Chicago, IL"}, {"region": "newark-usa-1", "available": true, "display_name": "US, Newark, NJ"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A16x4", "cloud_instance_type": "vcg-a16-24c-256g-64vram", "memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1200, "vcpus": 24, "num_gpus": 4, "gpu_type": "A16", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 205.0, "deployment_type": "vm", "availability": [{"region": "sanjose-usa-1", "available": true, "display_name": "US, Silicon Valley, CA"}, {"region": "chicago-usa-1", "available": true, "display_name": "US, Chicago, IL"}, {"region": "newark-usa-1", "available": true, "display_name": "US, Newark, NJ"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": true, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": true, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_plusx4", "cloud_instance_type": "gpu_4x_A6000_high_perf", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_plusx4", "cloud_instance_type": "gpu_4x_A6000_high_perf", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_plusx4", "cloud_instance_type": "gpu_4x_A6000_high_perf", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_basex4", "cloud_instance_type": "gpu_4x_a6000_low_ram", "memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": true, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_basex4", "cloud_instance_type": "gpu_4x_a6000_low_ram", "memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": true, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_basex4", "cloud_instance_type": "gpu_4x_a6000_low_ram", "memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": true, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000_basex4", "cloud_instance_type": "gpu_4x_a6000_low_ram", "memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 228.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": true, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000x4_NVLINK", "cloud_instance_type": "gpu_4x_a6000_nvlink", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 232.0, "deployment_type": "vm", "availability": [{"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}, {"region": "wichita-usa-1", "available": true, "display_name": "US, Wichita, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A6000x4_NVLINK", "cloud_instance_type": "gpu_4x_a6000_nvlink", "memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1024, "vcpus": 30, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 232.0, "deployment_type": "vm", "availability": [{"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}, {"region": "wichita-usa-1", "available": true, "display_name": "US, Wichita, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A6000x4", "cloud_instance_type": "gpu_4x_a6000", "memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 400, "storage_in_gb": 1024, "vcpus": 56, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "paperspace", "shade_instance_type": "A4000x4", "cloud_instance_type": "A4000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}, {"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A4000x4", "cloud_instance_type": "A4000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 320.0, "deployment_type": "vm", "availability": [{"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}, {"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "massedcompute", "shade_instance_type": "RTX6000Adax4", "cloud_instance_type": "gpu_4x_rtx6000ada", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "RTX6000Ada", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "RTX6000Ada", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 388.0, "deployment_type": "vm", "availability": [{"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "L40x4", "cloud_instance_type": "gpu_4x_l40", "memory_in_gb": 768, "storage_in_gb": 2500, "vcpus": 100, "num_gpus": 4, "gpu_type": "L40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 768, "storage_in_gb": 2500, "vcpus": 100, "num_gpus": 4, "gpu_type": "L40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 396.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "crusoe", "shade_instance_type": "A40x4", "cloud_instance_type": "a40.4x", "memory_in_gb": 240, "storage_in_gb": 3968, "vcpus": 24, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 240, "storage_in_gb": 3968, "vcpus": 24, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os"]}, "hourly_price": 440.0, "deployment_type": "vm", "availability": [{"region": "fairview-usa-1", "available": false, "display_name": "US, Fairview, OH"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "A100x4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 800, "storage_in_gb": 1024, "vcpus": 120, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 516.0, "deployment_type": "vm", "availability": [{"region": "chicago-usa-2", "available": false, "display_name": "US, Chicago, IL"}, {"region": "phoenix-usa-2", "available": false, "display_name": "US, Phoenix, AZ"}, {"region": "sanjose-usa-2", "available": false, "display_name": "US, San Jose, Ca"}, {"region": "dulles-usa-1", "available": false, "display_name": "US, Dulles, VA"}, {"region": "saltlakecity-usa-1", "available": false, "display_name": "US, Salt Lake City, UT"}, {"region": "dallas-usa-2", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "L40Sx4", "cloud_instance_type": "gpu_4x_l40s", "memory_in_gb": 512, "storage_in_gb": 2500, "vcpus": 88, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 512, "storage_in_gb": 2500, "vcpus": 88, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 520.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "L40Sx4", "cloud_instance_type": "gpu_4x_l40s", "memory_in_gb": 512, "storage_in_gb": 2500, "vcpus": 88, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 512, "storage_in_gb": 2500, "vcpus": 88, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 520.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "paperspace", "shade_instance_type": "A5000x4", "cloud_instance_type": "A5000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 568.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A5000x4", "cloud_instance_type": "A5000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 568.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "crusoe", "shade_instance_type": "A100x4", "cloud_instance_type": "a100.4x", "memory_in_gb": 480, "storage_in_gb": 2048, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 2048, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os"]}, "hourly_price": 580.0, "deployment_type": "vm", "availability": [{"region": "fairview-usa-1", "available": false, "display_name": "US, Fairview, OH"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "crusoe", "shade_instance_type": "L40Sx4", "cloud_instance_type": "l40s-48gb.4x", "memory_in_gb": 588, "storage_in_gb": 128, "vcpus": 32, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 588, "storage_in_gb": 128, "vcpus": 32, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os"]}, "hourly_price": 580.0, "deployment_type": "vm", "availability": [{"region": "houston-usa-2", "available": true, "display_name": "US, Houston, TX"}, {"region": "culpeper-usa-1", "available": true, "display_name": "US, Culpeper, VA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "crusoe", "shade_instance_type": "L40Sx4", "cloud_instance_type": "l40s-48gb.4x", "memory_in_gb": 588, "storage_in_gb": 128, "vcpus": 32, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 588, "storage_in_gb": 128, "vcpus": 32, "num_gpus": 4, "gpu_type": "L40S", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os"]}, "hourly_price": 580.0, "deployment_type": "vm", "availability": [{"region": "houston-usa-2", "available": true, "display_name": "US, Houston, TX"}, {"region": "culpeper-usa-1", "available": true, "display_name": "US, Culpeper, VA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 620.0, "deployment_type": "vm", "availability": [{"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}, {"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 620.0, "deployment_type": "vm", "availability": [{"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}, {"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "gpu_4x_a100", "memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 256, "storage_in_gb": 1024, "vcpus": 54, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 620.0, "deployment_type": "vm", "availability": [{"region": "wichita-usa-1", "available": false, "display_name": "US, Wichita, KS"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}, {"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "crusoe", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "a100-80gb.4x", "memory_in_gb": 480, "storage_in_gb": 2048, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 2048, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os"]}, "hourly_price": 660.0, "deployment_type": "vm", "availability": [{"region": "fairview-usa-1", "available": false, "display_name": "US, Fairview, OH"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A40x4", "cloud_instance_type": "vcg-a40-96c-480g-192vram", "memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 744.0, "deployment_type": "vm", "availability": [{"region": "newark-usa-1", "available": false, "display_name": "US, Newark, NJ"}, {"region": "sanjose-usa-1", "available": false, "display_name": "US, Silicon Valley, CA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "vultr", "shade_instance_type": "A40x4", "cloud_instance_type": "vcg-a40-96c-480g-192vram", "memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 1400, "vcpus": 96, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.0_shade_os", "ubuntu20.04_cuda12.0_shade_os", "ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 744.0, "deployment_type": "vm", "availability": [{"region": "newark-usa-1", "available": false, "display_name": "US, Newark, NJ"}, {"region": "sanjose-usa-1", "available": false, "display_name": "US, Silicon Valley, CA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "paperspace", "shade_instance_type": "A6000x4", "cloud_instance_type": "A6000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 772.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A6000x4", "cloud_instance_type": "A6000x4", "memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 180, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 772.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "voltagepark", "shade_instance_type": "H100_sxm5x4", "cloud_instance_type": "h100-sxm5-80gb", "memory_in_gb": 464, "storage_in_gb": 4800, "vcpus": 104, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "nvlink": false, "configuration": {"memory_in_gb": 464, "storage_in_gb": 4800, "vcpus": 104, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu24.04_cuda12.4_shade_os"]}, "hourly_price": 796.0, "deployment_type": "vm", "availability": [{"region": "dallas-usa-5", "available": true, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "latitude", "shade_instance_type": "H100x4", "cloud_instance_type": "g3-h100-medium", "memory_in_gb": 768, "storage_in_gb": 7600, "vcpus": 128, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 768, "storage_in_gb": 7600, "vcpus": 128, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu22.04", "ubuntu24.04"]}, "hourly_price": 897.0, "deployment_type": "baremetal", "availability": [{"region": "dallas-usa-1", "available": false, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "paperspace", "shade_instance_type": "V100_32Gx4", "cloud_instance_type": "V100-32Gx4", "memory_in_gb": 120, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "V100_32G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 120, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "V100_32G", "interconnect": "pcie", "vram_per_gpu_in_gb": 32, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 936.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "V100_32Gx4", "cloud_instance_type": "V100-32Gx4", "memory_in_gb": 120, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "V100_32G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 120, "storage_in_gb": 1000, "vcpus": 32, "num_gpus": 4, "gpu_type": "V100_32G", "interconnect": "pcie", "vram_per_gpu_in_gb": 32, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 936.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": true, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "imwt", "shade_instance_type": "H100x4", "cloud_instance_type": "gpu_4x_h100", "memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 992.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-2", "available": true, "display_name": "US, Central"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "H100x4", "cloud_instance_type": "gpu_4x_h100", "memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 1192.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "H100x4", "cloud_instance_type": "gpu_4x_h100", "memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 1192.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "massedcompute", "shade_instance_type": "H100x4", "cloud_instance_type": "gpu_4x_h100", "memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 64, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 1192.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-3", "available": false, "display_name": "US, Des Moines, IA"}, {"region": "desmoines-usa-1", "available": true, "display_name": "US, Des Moines, IA"}, {"region": "kansascity-usa-1", "available": false, "display_name": "US, Kansas City, KS"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "H100_sxm5x4", "cloud_instance_type": "gpu_4x_h100_sxm5", "memory_in_gb": 900, "storage_in_gb": 11264, "vcpus": 104, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "nvlink": false, "configuration": {"memory_in_gb": 900, "storage_in_gb": 11264, "vcpus": 104, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 1236.0, "deployment_type": "vm", "availability": [{"region": "austin-usa-1", "available": true, "display_name": "US, Austin, TX"}, {"region": "dallas-usa-3", "available": true, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "lambdalabs", "shade_instance_type": "H100_sxm5x4", "cloud_instance_type": "gpu_4x_h100_sxm5", "memory_in_gb": 900, "storage_in_gb": 11264, "vcpus": 104, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "nvlink": false, "configuration": {"memory_in_gb": 900, "storage_in_gb": 11264, "vcpus": 104, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os"]}, "hourly_price": 1236.0, "deployment_type": "vm", "availability": [{"region": "austin-usa-1", "available": true, "display_name": "US, Austin, TX"}, {"region": "dallas-usa-3", "available": true, "display_name": "US, Dallas, TX"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "paperspace", "shade_instance_type": "A100x4", "cloud_instance_type": "A100x4", "memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 1244.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": false, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A100x4", "cloud_instance_type": "A100x4", "memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100", "interconnect": "pcie", "vram_per_gpu_in_gb": 40, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 1244.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": false, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "massedcompute", "shade_instance_type": "H100_nvlx4_NVLINK", "cloud_instance_type": "gpu_4x_h100_nvl_nvlink", "memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 80, "num_gpus": 4, "gpu_type": "H100_nvl", "interconnect": "pcie", "nvlink": true, "configuration": {"memory_in_gb": 512, "storage_in_gb": 5000, "vcpus": 80, "num_gpus": 4, "gpu_type": "H100_nvl", "interconnect": "pcie", "vram_per_gpu_in_gb": 94, "os_options": ["ubuntu22.04_cuda12.6_shade_os"]}, "hourly_price": 1264.0, "deployment_type": "vm", "availability": [{"region": "desmoines-usa-1", "available": false, "display_name": "US, Des Moines, IA"}], "boot_time": {"min_boot_in_sec": 300, "max_boot_in_sec": 600}}, {"cloud": "paperspace", "shade_instance_type": "A100_sxm4_80Gx4", "cloud_instance_type": "A100-80Gx4", "memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 1288.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": false, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}, {"cloud": "paperspace", "shade_instance_type": "A100_sxm4_80Gx4", "cloud_instance_type": "A100-80Gx4", "memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "nvlink": true, "configuration": {"memory_in_gb": 360, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "sxm4", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 1288.0, "deployment_type": "vm", "availability": [{"region": "santaclara-usa-1", "available": false, "display_name": "US, Santa Clara, CA"}, {"region": "newyork-usa-1", "available": false, "display_name": "US, New York, NY"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 720}}]