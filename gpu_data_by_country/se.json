[{"cloud": "cudo", "shade_instance_type": "A40x4", "cloud_instance_type": "ice-lake-a40-compute", "memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 180.0, "deployment_type": "vm", "availability": [{"region": "stockholm-sweden-2", "available": false, "display_name": "SE, Stockholm"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}, {"cloud": "cudo", "shade_instance_type": "A4000x4", "cloud_instance_type": "epyc-milan-rtx-a4000", "memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A4000", "interconnect": "pcie", "vram_per_gpu_in_gb": 16, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 187.0, "deployment_type": "vm", "availability": [{"region": "smedjebacken-sweden-1", "available": false, "display_name": "SE, Smedjebacken"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}, {"cloud": "cudo", "shade_instance_type": "A6000x4", "cloud_instance_type": "ice-lake-rtx-a6000", "memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 234.0, "deployment_type": "vm", "availability": [{"region": "stockholm-sweden-2", "available": false, "display_name": "SE, Stockholm"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}]