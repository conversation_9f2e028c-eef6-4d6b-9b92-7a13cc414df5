[{"cloud": "cudo", "shade_instance_type": "A40x4", "cloud_instance_type": "ice-lake-a40-compute", "memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 180.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-4", "available": false, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}, {"cloud": "cudo", "shade_instance_type": "A5000x4", "cloud_instance_type": "ice-lake-rtx-a5000", "memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 96, "storage_in_gb": 1000, "vcpus": 24, "num_gpus": 4, "gpu_type": "A5000", "interconnect": "pcie", "vram_per_gpu_in_gb": 24, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 194.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-4", "available": true, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}, {"cloud": "hyperstack", "shade_instance_type": "A6000x4", "cloud_instance_type": "n3-RTX-A6000x4", "memory_in_gb": 232, "storage_in_gb": 750, "vcpus": 124, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 232, "storage_in_gb": 750, "vcpus": 124, "num_gpus": 4, "gpu_type": "A6000", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 200.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-2", "available": false, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "hyperstack", "shade_instance_type": "L40x4", "cloud_instance_type": "n3-L40x4", "memory_in_gb": 232, "storage_in_gb": 3300, "vcpus": 126, "num_gpus": 4, "gpu_type": "L40", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 232, "storage_in_gb": 3300, "vcpus": 126, "num_gpus": 4, "gpu_type": "L40", "interconnect": "pcie", "vram_per_gpu_in_gb": 48, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 400.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-2", "available": false, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "hyperstack", "shade_instance_type": "A100_80Gx4", "cloud_instance_type": "n3-A100x4", "memory_in_gb": 480, "storage_in_gb": 3300, "vcpus": 124, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 480, "storage_in_gb": 3300, "vcpus": 124, "num_gpus": 4, "gpu_type": "A100_80G", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 540.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-2", "available": false, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 480, "max_boot_in_sec": 600}}, {"cloud": "hyperstack", "shade_instance_type": "H100x4", "cloud_instance_type": "n3-H100x4", "memory_in_gb": 720, "storage_in_gb": 3300, "vcpus": 124, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "nvlink": false, "configuration": {"memory_in_gb": 720, "storage_in_gb": 3300, "vcpus": 124, "num_gpus": 4, "gpu_type": "H100", "interconnect": "pcie", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.2_shade_os", "ubuntu20.04_cuda12.2_shade_os", "ubuntu22.04", "ubuntu20.04"]}, "hourly_price": 760.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-2", "available": true, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 1200}}, {"cloud": "cudo", "shade_instance_type": "H100x4", "cloud_instance_type": "sapphire-rapids-h100", "memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "nvlink": false, "configuration": {"memory_in_gb": 192, "storage_in_gb": 1000, "vcpus": 48, "num_gpus": 4, "gpu_type": "H100", "interconnect": "sxm5", "vram_per_gpu_in_gb": 80, "os_options": ["ubuntu22.04_cuda12.4_shade_os", "ubuntu24.04"]}, "hourly_price": 1142.0, "deployment_type": "vm", "availability": [{"region": "montreal-canada-4", "available": false, "display_name": "CA, Montreal"}], "boot_time": {"min_boot_in_sec": 600, "max_boot_in_sec": 900}}]