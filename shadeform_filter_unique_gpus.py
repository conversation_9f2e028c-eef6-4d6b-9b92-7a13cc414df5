import pandas as pd

# Read the input CSV file
input_csv = 'all_gpu_instances.csv'  # Replace with your actual CSV file path
df = pd.read_csv(input_csv)

# Sort by hourly_price and country before deduplication
df = df.sort_values(by=['hourly_price', 'country'], ascending=[True, True])

# Define the columns for uniqueness
unique_columns = ['cloud', 'country', 'gpu_type', 'deployment_type']

# Get the first occurrence of each unique combination
result_df = df.drop_duplicates(subset=unique_columns, keep='first').reset_index(drop=True)

# Apply filtering condition:
# Remove rows where:
# vram_per_gpu_in_gb == 0 AND memory_in_gb <= 40 AND storage_in_gb <= 250 AND available == False
filter_condition = (
    (result_df['vram_per_gpu_in_gb'] == 0) |
    (result_df['memory_in_gb'] <= 40) |
    (result_df['storage_in_gb'] <= 250) |
    (result_df['available'] == False)
)
result_df = result_df[~filter_condition]

# Columns to remove (drop only if they exist in the dataframe)
columns_to_remove = ['cloud_instance_type', 'interconnect', 'availability_region', 'display_name']
result_df = result_df.drop(columns=[col for col in columns_to_remove if col in result_df.columns])

# Save the result to a new CSV file
output_csv = 'unique_gpu_instances.csv'
result_df.to_csv(output_csv, index=False)

print(f"Filtered unique GPU instances saved to {output_csv}")
