{
  "python.testing.pytestArgs": [
    "tests"
  ],
  "notebook.inlineValues": true,
  "python.testing.pytestEnabled": true,
  "[python]": {
      "editor.rulers": [
        {
          "column": 89
        }
      ],
      // We use Ruff as formatter
      "editor.defaultFormatter": "charliermarsh.ruff",
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
          "source.fixAll": "always"
      },
      "editor.minimap.maxColumn": 89,
      "editor.matchBrackets": "always",
      "editor.minimap.scale": 3,
  },
  "ruff.configuration": "pyproject.toml",
}