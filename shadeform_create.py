import requests
import os
import pandas as pd
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from dotenv import load_dotenv
load_dotenv()

API_KEY = os.getenv("SHADEFORM_API_KEY")
HEADER = {
    "X-API-KEY": f"{API_KEY}"
}
SFORM_PERF_KEY = os.getenv('SFORM_PERF_KEY')

# Thread-safe printing
print_lock = threading.Lock()

def safe_print(*args, **kwargs):
    """Thread-safe print function"""
    with print_lock:
        print(*args, **kwargs)

def create_instance(row_data, row_number):
    """Create a single instance using data from CSV row"""
    create_url = "https://api.shadeform.ai/v1/instances/create"

    payload = {
        "cloud": row_data['cloud'],
        "name": f"shadeform-test-{row_number}",
        "os": row_data['os_option'],
        "shade_cloud": True,
        "shade_instance_type": row_data['shade_instance_type'],
        "region": row_data['region'],
        "ssh_key_id": f"{SFORM_PERF_KEY}"
    }

    safe_print(f"Creating instance {row_number} with payload: {payload}")
    response = requests.post(create_url, json=payload, headers=HEADER)
    response.raise_for_status()
    return response.json()

def get_instance_info(instance_id):
    """Get instance information"""
    url = f"https://api.shadeform.ai/v1/instances/{instance_id}/info"
    response = requests.get(url, headers=HEADER)
    response.raise_for_status()
    return response.json()

def delete_instance(instance_id):
    """Delete an instance"""
    url = f"https://api.shadeform.ai/v1/instances/{instance_id}/delete"
    response = requests.post(url, headers=HEADER)
    response.raise_for_status()

def wait_for_instance_active(instance_id, max_wait_minutes=15):
    """Wait for instance to become active and return timing info"""
    safe_print(f"Waiting for instance {instance_id} to become active...")
    start_time = time.time()
    max_wait_seconds = max_wait_minutes * 60

    while time.time() - start_time < max_wait_seconds:
        try:
            info = get_instance_info(instance_id)
            safe_print(f"Instance {instance_id} status: {info.get('status', 'unknown')}")

            if info.get('active_at') is not None:
                safe_print(f"Instance {instance_id} is now active!")
                return info

        except Exception as e:
            safe_print(f"Error checking instance {instance_id}: {e}")

        time.sleep(30)  # Wait 30 seconds between checks

    raise TimeoutError(f"Instance {instance_id} did not become active within {max_wait_minutes} minutes")

def calculate_time_difference(created_at_str, active_at_str):
    """Calculate difference between created_at and active_at in seconds"""
    try:
        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
        active_at = datetime.fromisoformat(active_at_str.replace('Z', '+00:00'))
        time_diff = float((active_at - created_at).total_seconds())
        return time_diff, time_diff / 60.0
    except Exception as e:
        safe_print(f"Error calculating time difference: {e}")
        return None

def process_single_instance(row_data, row_number):
    """Process a single instance from creation to deletion"""
    instance_id = None
    try:
        safe_print(f"\n=== Processing Instance {row_number} ===")

        # Step 1: Create instance
        create_response = create_instance(row_data, row_number)
        instance_id = create_response['id']
        safe_print(f"Created instance {row_number} with ID: {instance_id}")

        # Step 2: Wait for instance to become active
        active_info = wait_for_instance_active(instance_id)

        # Step 3: Extract required information
        created_at = active_info.get('created_at')
        active_at = active_info.get('active_at')
        ssh_user = active_info.get('ssh_user')
        ssh_port = active_info.get('ssh_port')
        cost_estimate = active_info.get('cost_estimate')

        # Calculate time difference
        time_diff_seconds, time_diff_minutes = calculate_time_difference(created_at, active_at)

        # Step 4: Prepare result row (original CSV data + new fields)
        result_row = row_data.to_dict()
        result_row.update({
            'instance_id': instance_id,
            'created_at': created_at,
            'active_at': active_at,
            'ssh_user': ssh_user,
            'ssh_port': ssh_port,
            'cost_estimate': cost_estimate,
            'activation_time_seconds': time_diff_seconds,
            'activation_time_minutes': time_diff_minutes
        })

        # Step 5: Delete instance
        safe_print(f"Deleting instance {instance_id}...")
        delete_instance(instance_id)
        safe_print(f"Instance {instance_id} deleted successfully")

        return result_row

    except Exception as e:
        safe_print(f"Error processing instance {row_number}: {e}")
        # Still add partial result if we have instance_id
        result_row = row_data.to_dict()
        result_row.update({
            'instance_id': instance_id,
            'created_at': None,
            'active_at': None,
            'ssh_user': None,
            'ssh_port': None,
            'cost_estimate': None,
            'activation_time_seconds': None,
            'activation_time_minutes': None,
            'error': str(e)
        })
        return result_row

def main():
    # Read instances from CSV
    safe_print("Reading instances from CSV...")
    df = pd.read_csv('unique_gpu_instances.csv', index_col=False)
    safe_print(f"Found {len(df)} instances to process")

    # Prepare data for parallel processing
    instance_data = [(row, index + 1) for index, row in df.iterrows()]

    # Process instances in parallel
    results = []
    max_workers = min(len(df), 15)  # Limit to 5 concurrent instances or number of instances, whichever is smaller
    safe_print(f"Starting parallel processing with {max_workers} workers...")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_instance = {
            executor.submit(process_single_instance, row_data, row_number): row_number
            for row_data, row_number in instance_data
        }

        # Collect results as they complete
        for future in as_completed(future_to_instance):
            row_number = future_to_instance[future]
            try:
                result = future.result()
                results.append(result)
                safe_print(f"Completed processing instance {row_number}")
            except Exception as e:
                safe_print(f"Instance {row_number} generated an exception: {e}")

    # Save results to new CSV
    if results:
        results_df = pd.DataFrame(results)
        output_filename = f"instance_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        results_df.to_csv(output_filename, index=False)
        safe_print(f"\nResults saved to: {output_filename}")
        safe_print(f"Processed {len(results)} instances")

        # Print summary
        successful_results = [r for r in results if 'error' not in r]
        failed_results = [r for r in results if 'error' in r]
        safe_print(f"Successful: {len(successful_results)}, Failed: {len(failed_results)}")
    else:
        safe_print("No results to save")

if __name__ == "__main__":
    main()